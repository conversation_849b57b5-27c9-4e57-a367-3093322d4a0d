
/* CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
}

/* Header and Navbar Styling */
header {
    margin: 0;
    padding: 0;
}

.navbar {
    background-color: transparent !important;
    margin: 0;
    padding-top: 0;
    padding-bottom: 0;
}

.hero-section{
    background: url("../assets/hero.png") no-repeat center center/cover;
    min-height: 100vh;
    position: relative;
}

.hero-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #161C2D;
    opacity: 0.7;
    z-index: 1;
}